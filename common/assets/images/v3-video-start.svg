<svg width="163" height="163" viewBox="0 0 163 163" fill="none" xmlns="http://www.w3.org/2000/svg">
	<g filter="url(#filter0_f_0_878)">
		<rect x="20" y="20" width="123" height="123" rx="61.5" fill="#838ECB" fill-opacity="0.1" />
	</g>
	<g filter="url(#filter1_f_0_878)">
		<rect x="30" y="21" width="103" height="103" rx="51.5" fill="#3B4BA3" />
	</g>
	<g filter="url(#filter2_f_0_878)">
		<rect x="29.5" y="10.5" width="104" height="103" rx="51.5" stroke="url(#paint0_linear_0_878)" />
	</g>
	<rect x="29.5" y="10.5" width="104" height="103" rx="51.5" fill="url(#paint1_linear_0_878)" />
	<rect x="29.5" y="10.5" width="104" height="103" rx="51.5" stroke="url(#paint2_linear_0_878)" />
	<g filter="url(#filter3_if_0_878)">
		<rect x="29.5" y="10.5" width="104" height="103" rx="51.5" stroke="url(#paint3_linear_0_878)" />
	</g>
	<g filter="url(#filter4_if_0_878)">
		<rect x="29.5" y="10.5" width="104" height="103" rx="51.5" stroke="url(#paint4_linear_0_878)" />
	</g>
	<g filter="url(#filter5_di_0_878)">
		<path d="M94.4847 60.7164C95.9448 61.4572 95.9448 63.5428 94.4847 64.2836L76.1549 73.5835C74.8245 74.2585 73.25 73.2918 73.25 71.7999L73.25 53.2001C73.25 51.7082 74.8245 50.7415 76.1549 51.4165L94.4847 60.7164Z" fill="white" />
		<path d="M94.4847 60.7164C95.9448 61.4572 95.9448 63.5428 94.4847 64.2836L76.1549 73.5835C74.8245 74.2585 73.25 73.2918 73.25 71.7999L73.25 53.2001C73.25 51.7082 74.8245 50.7415 76.1549 51.4165L94.4847 60.7164Z" fill="url(#paint5_linear_0_878)" />
		<path d="M94.2588 61.1621C95.3536 61.7178 95.3536 63.2822 94.2588 63.8379L75.9287 73.1377C74.9309 73.6439 73.75 72.9187 73.75 71.7998L73.75 53.2002C73.75 52.0813 74.9309 51.3561 75.9287 51.8623L94.2588 61.1621Z" stroke="url(#paint6_linear_0_878)" />
	</g>
	<defs>
		<filter id="filter0_f_0_878" x="0" y="0" width="163" height="163" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix" />
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
			<feGaussianBlur stdDeviation="10" result="effect1_foregroundBlur_0_878" />
		</filter>
		<filter id="filter1_f_0_878" x="10" y="1" width="143" height="143" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix" />
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
			<feGaussianBlur stdDeviation="10" result="effect1_foregroundBlur_0_878" />
		</filter>
		<filter id="filter2_f_0_878" x="23" y="4" width="117" height="116" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix" />
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
			<feGaussianBlur stdDeviation="3" result="effect1_foregroundBlur_0_878" />
		</filter>
		<filter id="filter3_if_0_878" x="24" y="5" width="115" height="114" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix" />
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
			<feOffset dx="2" dy="2" />
			<feGaussianBlur stdDeviation="0.5" />
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
			<feColorMatrix type="matrix" values="0 0 0 0 0.553861 0 0 0 0 0.63921 0 0 0 0 1 0 0 0 1 0" />
			<feBlend mode="normal" in2="shape" result="effect1_innerShadow_0_878" />
			<feGaussianBlur stdDeviation="2.5" result="effect2_foregroundBlur_0_878" />
		</filter>
		<filter id="filter4_if_0_878" x="26" y="7" width="111" height="110" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix" />
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
			<feOffset dx="2" dy="2" />
			<feGaussianBlur stdDeviation="0.5" />
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
			<feColorMatrix type="matrix" values="0 0 0 0 0.553861 0 0 0 0 0.63921 0 0 0 0 1 0 0 0 1 0" />
			<feBlend mode="normal" in2="shape" result="effect1_innerShadow_0_878" />
			<feGaussianBlur stdDeviation="1.5" result="effect2_foregroundBlur_0_878" />
		</filter>
		<filter id="filter5_di_0_878" x="63.25" y="39.1974" width="38.3298" height="38.6051" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix" />
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
			<feOffset dx="-2" dy="-4" />
			<feGaussianBlur stdDeviation="4" />
			<feComposite in2="hardAlpha" operator="out" />
			<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.6 0" />
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_0_878" />
			<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_0_878" result="shape" />
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
			<feOffset dx="2" dy="4" />
			<feGaussianBlur stdDeviation="2" />
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
			<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0" />
			<feBlend mode="normal" in2="shape" result="effect2_innerShadow_0_878" />
		</filter>
		<linearGradient id="paint0_linear_0_878" x1="81.5" y1="11" x2="81.5" y2="113" gradientUnits="userSpaceOnUse">
			<stop stop-color="#2E3150" stop-opacity="0" />
			<stop offset="1" stop-color="#6176F6" />
		</linearGradient>
		<linearGradient id="paint1_linear_0_878" x1="81.5" y1="11" x2="81.5" y2="113" gradientUnits="userSpaceOnUse">
			<stop offset="0.413118" />
			<stop offset="1" stop-color="#242D53" />
		</linearGradient>
		<linearGradient id="paint2_linear_0_878" x1="81.5" y1="11" x2="81.5" y2="113" gradientUnits="userSpaceOnUse">
			<stop stop-color="white" stop-opacity="0" />
			<stop offset="1" stop-color="white" stop-opacity="0.5" />
		</linearGradient>
		<linearGradient id="paint3_linear_0_878" x1="81.5" y1="88.5537" x2="81.5" y2="113" gradientUnits="userSpaceOnUse">
			<stop stop-color="white" stop-opacity="0" />
			<stop offset="0.802885" stop-color="white" />
		</linearGradient>
		<linearGradient id="paint4_linear_0_878" x1="81.5" y1="88.5537" x2="81.5" y2="113" gradientUnits="userSpaceOnUse">
			<stop stop-color="white" stop-opacity="0" />
			<stop offset="1" stop-color="white" />
		</linearGradient>
		<linearGradient id="paint5_linear_0_878" x1="84" y1="61" x2="69.5" y2="77.5" gradientUnits="userSpaceOnUse">
			<stop stop-color="white" />
			<stop offset="1" stop-opacity="0.2" />
		</linearGradient>
		<linearGradient id="paint6_linear_0_878" x1="88" y1="57" x2="76" y2="69.5" gradientUnits="userSpaceOnUse">
			<stop offset="0.181988" stop-color="white" stop-opacity="0.5" />
			<stop offset="1" stop-opacity="0.2" />
		</linearGradient>
	</defs>
</svg>
