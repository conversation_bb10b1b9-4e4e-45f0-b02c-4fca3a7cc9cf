import _ from 'lodash';

const CHATDOC_URLS = [
  '/chatdoc/',
  'https://chatdoc.dev.cheftin.cn/chatdoc/',
  'https://chatdoc.com/chatdoc/',
];

export const getChatDOCLink = (link, params = {}) => {
  params = _.omitBy(params, (v) => _.isUndefined(v) || _.isNull(v) || v === '');

  const search = new URLSearchParams();
  Object.entries({ ...params }).forEach(([k, v]) => {
    search.append(k, v);
  });
  const searchString = search.toString();
  if (searchString) {
    if (CHATDOC_URLS.includes(link)) {
      return `${link}#/upload?${searchString}`;
    } else {
      return `${link}?${searchString}`;
    }
  } else {
    return link;
  }
};
