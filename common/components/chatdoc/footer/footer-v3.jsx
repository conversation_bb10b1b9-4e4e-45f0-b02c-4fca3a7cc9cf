import React from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import { getUrlWithProduct } from '../../../urls';
import { useGlobalContext } from '../../../hooks/useGlobalHook';
import './footer.less';

const FooterV3 = () => {
  const { logo, footerData } = useGlobalContext();

  return (
    <footer className="footer-v3-wrapper">
      <Container>
        <Row>
          <Col lg={6} md={12} className="footer-brand-section">
            <div className="footer-brand">
              <div className="brand-logo">
                <img src={logo} alt={`ChatDoc Logo`} className="brand-icon" />
                <span className="brand-text">ChatDOC</span>
              </div>
              <p className="footer-copyright">
                ChatDOC.com &copy; {new Date().getFullYear()}. All Rights
                Reserved
              </p>
            </div>
          </Col>

          <Col lg={6} md={12} className="footer-links-section">
            <Row>
              {footerData.map((group, groupIndex) => (
                <Col lg={4} xs={6} key={groupIndex} className="footer-group">
                  <h6 className="footer-group-title">{group.title}</h6>
                  <ul className="footer-links-list">
                    {group.links.map((link, linkIndex) => (
                      <li key={linkIndex} className="footer-link-item">
                        <a
                          href={getUrlWithProduct(link.linkProduct, link.link)}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="footer-link">
                          {link.name}
                        </a>
                      </li>
                    ))}
                  </ul>
                </Col>
              ))}
            </Row>
          </Col>
        </Row>
      </Container>
    </footer>
  );
};

export default FooterV3;
