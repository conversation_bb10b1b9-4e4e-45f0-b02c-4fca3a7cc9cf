.footer-wrapper {
  position: relative;
  z-index: 0;
  padding-bottom: 60px;
  text-align: center;

  .footer-link {
    color: @dark-text-color;
    font-size: 20px;
    line-height: 36px;
    text-decoration: underline;
    text-underline-position: under;
  }

  .footer-point {
    padding: 0 3px;
    color: @dark-text-color;
    font-size: 20px;

    &:last-child {
      display: none;
    }
  }

  .footer-bg,
  .footer-bg-mini {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: -1;
    width: 100%;
  }

  .footer-bg-mini {
    display: none;
  }
}

@media (max-width: @mini-screen-size) {
  .footer-wrapper {
    padding: 0 20px 24px;

    .footer-link {
      font-size: 14px;
      line-height: 36px;
    }

    .footer-point {
      padding: 0 2px;
      font-size: 14px;
    }

    .footer-bg {
      display: none;
    }

    .footer-bg-mini {
      display: block;
    }
  }
}

.footer-v3-wrapper {
  padding: 60px 0;
  color: #fff;
  background-color: #000;

  .footer-brand-section {
    display: flex;
    align-items: flex-start;
    height: 271px;

    .footer-brand {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 100%;

      .brand-logo {
        display: flex;
        align-items: center;
        margin-bottom: 20px;

        .brand-icon {
          width: 54px;
          margin-right: 15px;
        }

        .brand-text {
          color: #fff;
          font-weight: 500;
          font-size: 26px;
        }
      }

      .footer-copyright {
        margin: 0;
        color: rgba(255, 255, 255, 40%);
        font-size: 16px;
        line-height: 24px;
      }
    }
  }

  .footer-links-section {
    .footer-group {
      .footer-group-title {
        margin-bottom: 30px;
        color: #fff;
        font-size: 18px;
        text-transform: none;
      }

      .footer-links-list {
        margin: 0;
        padding: 0;
        list-style: none;

        .footer-link-item {
          margin-bottom: 12px;

          .footer-link {
            color: rgba(255, 255, 255, 40%);
            font-size: 18px;
            line-height: 1.5;
            text-decoration: none;
            transition: color 0.3s ease;

            &:hover {
              color: #fff;
              text-decoration: none;
            }
          }
        }
      }
    }
  }
}

@media (max-width: @small-screen-size) {
  .footer-v3-wrapper {
    position: relative;
    padding: 114px 0 120px;

    .footer-brand-section {
      height: auto;
      margin-bottom: 40px;
      text-align: center;

      .footer-copyright {
        position: absolute;
        bottom: 54px;
      }
    }

    .footer-links-section {
      .footer-group {
        margin-bottom: 40px;
      }
    }
  }
}
