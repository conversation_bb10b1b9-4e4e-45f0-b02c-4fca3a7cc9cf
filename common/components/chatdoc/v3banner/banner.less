.v3-banner-wrapper {
  padding: 180px 0 75px;
  background-color: transparent;
  background-image: none;

  .banner-row {
    align-items: center;

    .title-box {
      display: flex;
      align-items: center;
    }

    .banner-desc {
      margin-top: 50px;
      color: rgba(255, 255, 255, 60%);
      font-weight: 400;
      font-size: 26px;
      line-height: 28px;
    }

    .banner-left {
      padding: 0;
      width: 58%;
    }

    .banner-right {
      width:42%;
      padding: 0;
      .video-box {
        position: relative;
        width: 100%;
        height: 100%;

        .video-bg {
          width: 100%;
          height: 100%;

          &::before {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 0;
            width: 171px;
            height: 185px;
            background: radial-gradient(
              ellipse at center,
              rgba(75, 183, 90, 30%) 60%,
              transparent 100%
            );
            opacity: 1;
            filter: blur(100px);
            content: '';
          }

          &::after {
            position: absolute;
            right: -45px;
            bottom: 85px;
            z-index: 0;
            width: 89px;
            height: 186px;
            background: radial-gradient(
              ellipse at center,
              #627ae6 30%,
              transparent 100%
            );
            opacity: 0.3;
            filter: blur(50px);
            content: '';
          }

          .video-bg-img {
            position: relative;
            z-index: 1;
            width: 100%;
            height: auto;
          }

          .video-bg-blue-line {
            position: absolute;
            right: 0;
            bottom: 100px;
            z-index: 1;
            width: 1px;
            height: 117px;
            background: linear-gradient(
              to bottom,
              rgba(98, 122, 230, 0%) 0%,
              #627ae6 50%,
              rgba(98, 122, 230, 0%) 100%
            );
          }
        }
      }
    }
  }
}

@media (max-width: @small-screen-size) {
  .v3-banner-wrapper {
    padding: 70px 0;

    .banner-row {
      flex-direction: column-reverse;

      .banner-left {
        margin-top: 50px;
        width: 100%;
      }

      .banner-desc {
        margin-top: 30px;
        font-size: 20px;
      }

      .banner-right {
        width: 100%;
        .video-box {
          .video-bg {
            .video-bg-blue-line {
              bottom: 30px;
            }

            &::after {
              display: none;
            }
          }
        }
      }
    }
  }
}
