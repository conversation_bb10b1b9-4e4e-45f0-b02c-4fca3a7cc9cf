import React, { useState } from 'react';
import { useGlobalContext } from '../../../hooks/useGlobalHook';
import XGPlayerVideo from '../../video/xgplayer-video/xgplayer-video';
import './video-tabs.less';

const VideoTabs = () => {
  const [activeTab, setActiveTab] = useState(0);
  const { allBannerVideoData } = useGlobalContext();

  return (
    <div className="video-tabs-box">
      <div className="video-tabs">
        {allBannerVideoData.map((video, idx) => (
          <button
            key={video.name}
            className={`video-tab-btn${activeTab === idx ? ' active' : ''}`}
            onClick={() => setActiveTab(idx)}>
            {video.name}
          </button>
        ))}
      </div>
      <div className="video-player-box">
        <XGPlayerVideo
          className="banner-video"
          src={allBannerVideoData[activeTab].video.publicURL}
          poster={allBannerVideoData[activeTab].poster}
          isV3
        />
      </div>
    </div>
  );
};

export default VideoTabs;
