import React from 'react';
import { GatsbyImage } from 'gatsby-plugin-image';
import { useGlobalContext } from '../../../hooks/useGlobalHook';
import { useMobilePageContext } from '../../../hooks/useMobilePageContext';
import CommonSwiper from '../../swiper/common-swiper';
import './customer.less';

const Customer = () => {
  const { allCustomersData } = useGlobalContext();
  const isMobile = useMobilePageContext();
  return (
    <div className="customer-container">
      {isMobile ? (
        <CommonSwiper
          className="customer-swiper"
          swiperData={allCustomersData.nodes}
          slidesPerView={2.7}
          spaceBetween={10}
          swiperSlideChildren={(item) => (
            <div className="customer-btn">
              <GatsbyImage
                image={item.childImageSharp.gatsbyImageData}
                alt={item.name}
                className="customer-image"
              />
            </div>
          )}
        />
      ) : (
        allCustomersData.nodes.map((item, index) => (
          <div className="customer-btn" key={index}>
            <GatsbyImage
              image={item.childImageSharp.gatsbyImageData}
              alt={item.name}
              className="customer-image"
            />
          </div>
        ))
      )}
    </div>
  );
};

export default Customer;
