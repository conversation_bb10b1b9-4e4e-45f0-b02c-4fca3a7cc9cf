import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

import './title-typewriter.less';

const TYPING_SPEED = 120;
const DELETING_SPEED = 60;
const PAUSE_TIME = 1200;

const TitleTypewriter = ({ bannerTitle }) => {
  const [text, setText] = useState('');
  const [itemIndex, setItemIndex] = useState(0);
  const [charIndex, setCharIndex] = useState(0);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    let timer;
    const currentItem = bannerTitle[itemIndex] || '';

    if (!isDeleting && charIndex < currentItem.length) {
      timer = setTimeout(() => {
        setText(currentItem.slice(0, charIndex + 1));
        setCharIndex(charIndex + 1);
      }, TYPING_SPEED);
    } else if (!isDeleting && charIndex === currentItem.length) {
      timer = setTimeout(() => setIsDeleting(true), PAUSE_TIME);
    } else if (isDeleting && charIndex > 0) {
      timer = setTimeout(() => {
        setText(currentItem.slice(0, charIndex - 1));
        setCharIndex(charIndex - 1);
      }, DELETING_SPEED);
    } else if (isDeleting && charIndex === 0) {
      timer = setTimeout(() => {
        setIsDeleting(false);
        setItemIndex((itemIndex + 1) % bannerTitle.length);
      }, TYPING_SPEED);
    }
    return () => clearTimeout(timer);
  }, [bannerTitle, itemIndex, charIndex, isDeleting]);

  return (
    <div className="typewriter-wrapper">
      <span className="typewriter-text">{text}</span>
      <span className="typewriter-cursor">|</span>
    </div>
  );
};

TitleTypewriter.propTypes = {
  bannerTitle: PropTypes.array.isRequired,
};

export default TitleTypewriter;
