import React, { useState, useEffect, useCallback, useRef } from 'react';
import PropTypes from 'prop-types';
import { Container, Navbar } from 'react-bootstrap';
import Navs from './navs';
import NavBrand from './nav-brand';
import ProjectButton from '../button/project-button';
import ProductHunt from '../productHunt/product-hunt';
import BrowserDropDown from './browser-dropdown';
import menuSvg from '../../../../common/assets/images/menu-mobile.svg';
import menuV3Svg from '../../../../common/assets/images/menu-v3-mobile.svg';
import closeSvg from '../../../../common/assets/images/close.svg';
import whiteCloseSvg from '../../../../common/assets/images/white-close.svg';
import { useGlobalContext } from '../../../hooks/useGlobalHook';
import { useHandleResize } from '../../../hooks/useHandleResizeHook';
import { useGetUrlQueryData } from '../../../hooks/useUrlQueryHook';
import { getChatDOCLink } from '../../../utils/chatdoc/chatdoc-link';
import { useMobilePageContext } from '../../../hooks/useMobilePageContext';
import classnames from 'classnames';
import './header.less';

const Header = ({
  isIndexPage,
  projectButtonHref,
  currentHideFeatureInHeader,
  isV3,
}) => {
  const navbarTogglerRef = useRef();
  const src = useGetUrlQueryData('src');
  const { hideProductHunt, defaultSrc } = useGlobalContext();
  const [isShowShadow, setIsShowShadow] = useState(false);
  const [collapseOpen, setCollapseOpen] = useState(false);
  const [announcementHeight, setAnnouncementHeight] = useState(0);

  const handleCloseCollapse = () => {
    setCollapseOpen(false);
  };

  const stopPropagation = (e) => {
    e.stopPropagation();
  };

  const handleScroll = (e) => {
    const scrollTop = e.srcElement.scrollingElement.scrollTop;
    setIsShowShadow(!!scrollTop);
  };

  const showNavbarToggler = () => {
    if (navbarTogglerRef.current) {
      navbarTogglerRef.current.style.opacity = '1';
    }
  };

  useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    if (document.readyState === 'complete') {
      showNavbarToggler();
    } else {
      window.addEventListener('load', showNavbarToggler);
      return () => window.removeEventListener('load', showNavbarToggler);
    }
  }, []);

  const getAnnouncementHeight = useCallback(() => {
    const AnnouncementDom = document.querySelector('.announcement-wrapper');
    const suggestBrowserTipDom = document.querySelector('.suggest-browser-tip');
    if (suggestBrowserTipDom && AnnouncementDom) {
      setAnnouncementHeight(AnnouncementDom.clientHeight);
    }
  }, []);

  useHandleResize(() => {
    getAnnouncementHeight();
  });

  const isMobile = useMobilePageContext();

  return (
    <header
      className={classnames({
        'header-wrapper': true,
        shadow: isShowShadow,
        'v3-header-wrapper': isV3,
      })}
      style={{ top: `${announcementHeight}px` }}>
      <Container>
        <Navbar className="header-navbar" expand="md">
          <NavBrand
            href={
              isIndexPage ? '' : getChatDOCLink('/', { src: src || defaultSrc })
            }
          />
          <div className="navbar-main">
            {!hideProductHunt && (
              <ProductHunt className="navbar-product-hunt" />
            )}
            {!currentHideFeatureInHeader && (
              <>
                <Navbar.Toggle
                  ref={navbarTogglerRef}
                  aria-controls="basic-navbar"
                  onClick={() => setCollapseOpen(!collapseOpen)}>
                  {isV3 ? (
                    <img
                      src={collapseOpen ? whiteCloseSvg : menuV3Svg}
                      alt="navbar"
                    />
                  ) : (
                    <img src={collapseOpen ? closeSvg : menuSvg} alt="navbar" />
                  )}
                </Navbar.Toggle>
                <Navbar.Collapse
                  id="basic-navbar"
                  in={collapseOpen}
                  onPointerDown={handleCloseCollapse}>
                  <div
                    className="navbar-dropdown"
                    onPointerDown={stopPropagation}>
                    <Navs handleCloseCollapse={handleCloseCollapse} />
                    {isV3 && isMobile && (
                      <div className="mobile-header-btns">
                        <ProjectButton
                          href={projectButtonHref}
                          isV3
                          text={'Try for Free'}
                        />
                      </div>
                    )}
                  </div>
                </Navbar.Collapse>
              </>
            )}
          </div>
          {isV3 ? (
            !isMobile && (
              <div className="v3-header-btns">
                <BrowserDropDown handleCloseCollapse={handleCloseCollapse} />
                <ProjectButton
                  href={projectButtonHref}
                  isV3
                  text={'Try for Free'}
                />
              </div>
            )
          ) : (
            <ProjectButton href={projectButtonHref} />
          )}
        </Navbar>
      </Container>
    </header>
  );
};

Header.propTypes = {
  isIndexPage: PropTypes.bool,
  currentHideFeatureInHeader: PropTypes.bool,
  isV3: PropTypes.bool,
};

export default Header;
