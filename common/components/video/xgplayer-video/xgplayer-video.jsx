import React, { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { GatsbyImage } from 'gatsby-plugin-image';
import { useMobilePageContext } from '../../../hooks/useMobilePageContext';
import startIcon from '../../../assets/images/video-start.svg';
import v3StartIcon from '../../../assets/images/v3-video-start.svg';
import classnames from 'classnames';
import Player from 'xgplayer';
import 'xgplayer/dist/index.min.css';
import './xgplayer-video.less';

const XGPlayerVideo = ({ className, src, poster, isV3 }) => {
  const xgplayerRef = useRef();
  const isMobile = useMobilePageContext();
  const [isBackgroundImageLoaded, setIsBackgroundImageLoaded] = useState(false);

  const addXGplayerObserver = (xgplayer) => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          let playPromise = xgplayer.play();
          if (playPromise) {
            playPromise
              .then(() => {
                if (entry.isIntersecting) {
                  xgplayer.currentTime = 0;
                  xgplayer.play();
                } else {
                  xgplayer.pause();
                }
              })
              .catch((err) => {
                console.error(err);
              });
          }
        });
      },
      { threshold: 0.8 },
    );

    observer.observe(xgplayerRef.current);
  };

  const addXGplayerObserverInMobile = (xgplayer) => {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (!entry.isIntersecting) {
          xgplayer.pause();
        }
      });
    });

    observer.observe(xgplayerRef.current);
  };

  useEffect(() => {
    if (isBackgroundImageLoaded) {
      const xgplayer = new Player({
        el: xgplayerRef.current,
        url: src,
        poster: poster.publicURL,
        fluid: true,
        playsinline: true,
        controls: isMobile,
        loop: !isMobile,
        closeVideoClick: !isMobile,
        autoplay: !isMobile,
        autoplayMuted: !isMobile,
        ignores: isMobile ? ['replay'] : ['start', 'enter'],
        icons: {
          startPlay: isV3 ? v3StartIcon : startIcon,
        },
        width: '100%',
        height: '100%',
      });

      if (!isMobile) {
        addXGplayerObserver(xgplayer);
      } else {
        addXGplayerObserverInMobile(xgplayer);
      }

      return () => {
        xgplayer.destroy();
      };
    }
  }, [poster, src, isMobile, isBackgroundImageLoaded, isV3]);

  useEffect(() => {
    const backgroundImage = new Image();
    backgroundImage.src = poster.publicURL;
    backgroundImage.onload = () => {
      setIsBackgroundImageLoaded(true);
    };
    backgroundImage.onerror = () => {
      setIsBackgroundImageLoaded(false);
    };
  }, [poster]);

  return (
    <div
      className={classnames({
        [className]: className,
        'xgplayer-container': true,
      })}>
      {isBackgroundImageLoaded && <div ref={xgplayerRef} />}
      <GatsbyImage
        className="poster"
        image={poster.childImageSharp.gatsbyImageData}
        alt={poster.name}
        style={{
          backgroundImage: `url(${poster.publicURL})`,
        }}
      />
    </div>
  );
};

XGPlayerVideo.defaultProps = {
  isV3: false,
};

XGPlayerVideo.propTypes = {
  src: PropTypes.string,
  poster: PropTypes.object,
  className: PropTypes.string,
  isV3: PropTypes.bool,
};

export default XGPlayerVideo;
