.common-swiper {
  .swiper-wrapper {
    align-items: center;
  }

  .swiper-slide {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .swiper-pagination {
    position: relative;
    bottom: 0;
    margin-top: 38px;
    line-height: 16px;
  }

  .swiper-pagination-bullet {
    width: 16px;
    height: 16px;
    margin: 0 8px !important;
    background-color: #9ea0a5;
  }

  .swiper-pagination-bullet-active {
    background-color: #6576db;
  }

  &.common-swiper--line {
    .swiper-pagination-bullet-line {
      display: inline-block;
      width: 32px;
      height: 4px;
      margin: 0 4px !important;
      background-color: #d9d9d9;
      border-radius: 2px;
      opacity: 1;
    }

    .swiper-pagination-bullet-line-active {
      background-color: #6576db;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .common-swiper {
    .swiper-pagination {
      line-height: 8px;
    }

    .swiper-pagination-bullet {
      width: 8px;
      height: 8px;
      margin: 0 4px !important;
    }

    &.common-swiper--line {
      .swiper-pagination-bullet-line {
        width: 24px;
        height: 3px;
        margin: 0 3px !important;
        border-radius: 1.5px;
      }
    }
  }
}
