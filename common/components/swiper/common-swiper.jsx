import React from 'react';
import PropTypes from 'prop-types';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Pagination, Autoplay, Navigation } from 'swiper';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import './common-swiper.less';
import classnames from 'classnames';

const CommonSwiper = ({
  className,
  swiperData,
  swiperSlideChildren,
  initialSlide,
  slidesPerView,
  slidesPerGroup,
  spaceBetween,
  direction,
  centeredSlides,
  onSlideChange,
  pagination,
  navigation,
}) => {
  return (
    <Swiper
      modules={[Pagination, Autoplay, Navigation]}
      initialSlide={initialSlide}
      slidesPerView={slidesPerView}
      slidesPerGroup={slidesPerGroup}
      spaceBetween={spaceBetween}
      direction={direction}
      navigation={navigation ? { clickable: true } : navigation}
      centeredSlides={centeredSlides}
      onSlideChange={onSlideChange}
      loop
      pagination={pagination ? { clickable: true } : pagination}
      autoplay={{
        delay: 3000,
        disableOnInteraction: false,
      }}
      className={classnames({
        'common-swiper': true,
        [className]: className,
      })}>
      {swiperData.map((item, index) => (
        <SwiperSlide key={index}>
          {swiperSlideChildren(item, index)}
        </SwiperSlide>
      ))}
    </Swiper>
  );
};

CommonSwiper.defaultProps = {
  initialSlide: 0,
  slidesPerView: 1,
  direction: 'horizontal',
  centeredSlides: false,
  onSlideChange: () => {},
  pagination: true,
  navigation: false,
};

CommonSwiper.propTypes = {
  className: PropTypes.string,
  swiperData: PropTypes.array,
  swiperSlideChildren: PropTypes.func,
  initialSlide: PropTypes.number,
  slidesPerView: PropTypes.number,
  slidesPerGroup: PropTypes.number,
  spaceBetween: PropTypes.number,
  direction: PropTypes.oneOf(['horizontal', 'vertical']),
  centeredSlides: PropTypes.bool,
  onSlideChange: PropTypes.func,
  pagination: PropTypes.bool,
  navigation: PropTypes.bool,
};

export default CommonSwiper;
