import { getUrlsMap } from '../utils';

const paths = {
  index: '/',
  indexEn: '/en/',
  aboutUs: '/about-us/',
  blog: '/blog/',
  blogDetail: '/blog-detail/',
  product: '/#product',
  base: '/?tabName=base',
  check: '/?tabName=check',
  write: '/?tabName=write',
  platform: '/?tabName=platform',
  supervision: '/application/supervision/',
  bank: '/application/bank/',
  brokerage: '/application/brokerage/',
  fund: '/application/fund/',
  BOC: '/case/BOC/',
  CGS: '/case/CGS/',
  HKEX: '/case/HKEX/',
  HTSC: '/case/HTSC/',
  connect: '/cooperation/connect/',
  student: '/cooperation/student/',
  explore: '/cooperation/explore/',
  academic: '/cooperation/academic/',
  material: '/cooperation/material/',
  foundry: '/cooperation/connect/?product=Foundry',
  dataIntelligence: '/data-intelligence/',
  aiPlatform: '/ai-platform/',
  aiPlatformWriting: '/ai-platform/?feature=writing',
  aiPlatformTable: '/ai-platform/?feature=table',
  aiPlatformCatalogue: '/ai-platform/?feature=catalogue',
  aiPlatformDocument: '/ai-platform/?feature=document',
  aiPlatformConnect: '/cooperation/connect/?product=AI Platform',
};

const envs = [
  { host: 'https://paodingai.dev.cheftin.cn', env: 'test' },
  { host: 'https://www.paodingai.com', env: 'prod' },
];

const urlsMap = getUrlsMap(envs, paths);

export { urlsMap };
