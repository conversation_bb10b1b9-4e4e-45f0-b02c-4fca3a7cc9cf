import { getUrlsMap } from '../utils';

const paths = {
  index: '/',
  guide: '/guide/',
  blog: '/blog/',
  changelog: '/log/',
  eduoffer: '/eduoffer/',
  policy: '/privacy_policy.html',
  terms: '/term_of_service.html',
  project: '/chatdoc/',
  api: '/chatdoc/#/api',
  signUp: '/chatdoc/#/sign-up',
  apiPricing: '/chatdoc/#/api/overview/pricing',
};

const envs = [
  { host: 'https://chatdoc.dev.cheftin.cn', env: 'test' },
  { host: 'https://staging.chatdoc.com/', env: 'staging' },
  { host: 'https://chatdoc.com', env: 'prod' },
];

const urlsMap = getUrlsMap(envs, paths);

export { urlsMap };
