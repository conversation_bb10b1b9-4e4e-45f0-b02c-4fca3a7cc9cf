#!/usr/bin/env bash

set -ex

# chatdoc
if [[ -n "${ENABLE_CHATDOC_CDN}" ]]; then
  rclone copy --ignore-existing -P --timeout 600s \
      --exclude=/index.html public-chatdoc-cdn/ aliyun-cdn-pdppt-com-oss:/cdn-pdppt/chatdoc/
  rsync -av --delete public-chatdoc-cdn/ ci@**********:/data/officialweb-test/chatdoc/
else
  rsync -av --delete public-chatdoc/ ci@**********:/data/officialweb-test/chatdoc/
fi

# pdfparser
rsync -av --delete public-pdfparser/ ci@**********:/data/officialweb-test/pdfparser/

# paodingjiewen
rsync -av --delete public-paodingjiewen/ ci@**********:/data/officialweb-test/chatdoc_cn/

# pdfchat
if [[ -n "${ENABLE_PDFCHAT_CDN}" ]]; then
  rclone copy --ignore-existing -P --timeout 600s \
      --exclude=/index.html public-pdfchat-cdn/ aliyun-cdn-pdppt-com-oss:/cdn-pdppt/chatdoc/
  rsync -av --delete public-pdfchat-cdn/ ci@**********:/data/officialweb-test/pdfchat/
else
  rsync -av --delete public-pdfchat/ ci@**********:/data/officialweb-test/pdfchat/
fi

# chatdb
rsync -av --delete public-chatdb/ ci@**********:/data/officialweb-test/chatdb/

# pdflux
if [[ -n "${ENABLE_PDFLUX_CDN}" ]]; then
  rclone copy --ignore-existing -P --timeout 600s \
      --exclude=/index.html --exclude=/**/*.zst --exclude=/*.zst --exclude=/**/*.gz --exclude=/*.gz \
      public-pdflux-cdn/ aliyun-cdn-paodingai-com-oss:/cdn-paodingai/pdflux/
  rsync -av --delete public-pdflux-cdn/ ci@**********:/data/officialweb-test/pdflux/
else
  rsync -av --delete public-pdflux/ ci@**********:/data/officialweb-test/pdflux/
fi

# paodingai
rsync -av --delete public-paodingai/ ci@**********:/data/officialweb-test/paodingai/

# calliper
rsync -av --delete public-calliper/ ci@**********:/data/officialweb-test/calliper/

# calliper_vip
rsync -av --exclude=/logo.png --delete public-calliper-vip/ ci@**********:/data/officialweb-test/calliper-vip/

# pdflux sdk
rsync -av --delete public-pdflux-sdk/ ci@**********:/data/officialweb-test/pdflux_sdk/

# grater vip
rsync -av --delete public-grater-vip/ ci@**********:/data/officialweb-test/grater/

# grater
rsync -av --delete public-grater/ ci@**********:/data/officialweb-test/grater-toc/

# semanmeter
rsync -av --delete public-semanmeter/ ci@**********:/data/officialweb-test/semanmeter/

# scriber
rsync -av --delete public-scriber/ ci@**********:/data/officialweb-test/scriber/

# autodoc
rsync -av --delete public-autodoc/ ci@**********:/data/officialweb-test/autodoc/official/

# autodoc mvip
rsync -av --delete public-autodoc-vip/ ci@c122:/data/autodoc_py3/autodoc-vip-front/
rsync -av --delete public-autodoc-vip/ farm@c11:/data/autodoc_overall_review_2/autodoc-vip-front/

# autodoc next
rsync -av --delete public-autodoc-next/ ci@**********:/data/officialweb-test/autodoc-next/official/

# glazer
rsync -av --delete public-glazer/ ci@c2:/data/glazer_test/official_front/

# hunter
rsync -av --delete public-hunter/ ci@**********:/data/officialweb-test/hunter/

# metalmesh
rsync -av --delete public-metalmesh/ ci@**********:/data/officialweb-test/metalmesh/

curl http://mm.paodingai.com/hooks/zxg3ncokc3yuxfymyrco7zctta \
  -H 'Content-Type: application/json; charset=utf-8' \
  -d @- <<EOF
{
  "text": "官网测试环境前端已更新:\`${GO_REVISION_PAODING_OFFICIALWEB:0:8}(${GO_MATERIAL_BRANCH_PAODING_OFFICIALWEB})\`",
  "props": {
  "card": "
| [AutoDoc官网](https://autodoc.dev.cheftin.cn/) |\n
| --- |\n
| [AutoDoc-next官网](https://autodoc-next.test.paodingai.com/) |\n
| [AutoDoc-vip官网](http://autodoc-tob-test.test.paodingai.com/) |\n
| [Calliper官网](https://calliper.dev.cheftin.cn/) |\n
| [Calliper-VIP官网](https://calliper-vip.dev.cheftin.cn/) |\n
| [ChatDB官网](https://chatdb.dev.cheftin.cn/) |\n
| [ChatDOC官网](https://chatdoc.dev.cheftin.cn/) |\n
| [Glazer官网](https://glazer.dev.cheftin.cn/) |\n
| [Grater官网](https://grater-vip.dev.cheftin.cn/) |\n
| [Grater个人版官网](https://grater.dev.cheftin.cn/) |\n
| [Hunter官网](https://hunter.dev.cheftin.cn/) |\n
| [MetalMesh官网](https://metalmesh.dev.cheftin.cn/) |\n
| [Paoding官网](https://paodingai.dev.cheftin.cn/) |\n
| [Paodingjiewen官网](https://paodingjiewen.dev.cheftin.cn/) |\n
| [PDFChat官网](https://pdfchat.dev.cheftin.cn/) |\n
| [PDFlux官网](https://pdflux.dev.cheftin.cn/) |\n
| [PDFlux SDK官网](https://pdflux-sdk.dev.cheftin.cn/) |\n
| [PDFParser官网](https://pdfparser.dev.cheftin.cn/) |\n
| [Semanmeter官网](https://semanmeter.dev.cheftin.cn/) |\n
| [Scriber官网](https://scriber.dev.cheftin.cn/) | \n"
  },
"channel": "official-website",
"icon_url": "http://res.cloudinary.com/kdr2/image/upload/c_crop,g_faces,h_240,w_240/v1454772214/misc/c3p0-001.jpg",
"username": "CI"
}
EOF
