.workflow-wrapper {
  margin-top: 387px;
  padding-bottom: 111px;
  text-align: center;

  .module-title {
    span {
      background: linear-gradient(86deg, #627ae6 40.34%, #d6b0bc 71.07%);
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .workflow-images-swiper {
    padding: 136px 20px 82px;

    .swiper-pagination {
      display: none;
    }
  }

  .workflow-swiper-content {
    padding: 10px;

    .swiper-slide {
      width: 407px;
      height: 464px;
      background: linear-gradient(130deg, #000 1%, #222 114.3%);
      border: 1px solid rgba(255, 255, 255, 10%);
      border-radius: 16px;
      transform: rotate(5deg) scale(0.85);
      opacity: 0;
      transition: all 0.3s;
    }

    .swiper-slide-prev,
    .swiper-slide-next {
      position: relative;
      top: 20px;
      right: 20px;
      opacity: 1;

      &::after {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 60%);
        border-radius: 16px;
        content: '';
      }
    }

    .swiper-slide-prev {
      right: auto;
      left: 20px;
      transform: rotate(-5deg) scale(0.85);
    }

    .swiper-slide-active {
      position: relative;
      z-index: 2;
      transform: rotate(0);
      transform: scale(1.16);
      opacity: 1;

      &::before {
        position: absolute;
        top: -1px;
        left: -1px;
        z-index: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #4bb75a 0%, transparent 20%) top
          left / 200px 200px no-repeat;
        border-radius: 18px;
        content: '';
      }

      &::after {
        position: absolute;
        top: -23px;
        left: -33px;
        z-index: 0;
        width: 140px;
        height: 117px;
        background: radial-gradient(
          ellipse at center,
          rgba(75, 183, 90, 30%) 60%,
          transparent 100%
        );
        filter: blur(70px);
        content: '';
      }

      .item-bottom-blue {
        position: absolute;
        right: -1px;
        bottom: 80px;
        z-index: 0;
        width: 1px;
        height: 117px;
        background: linear-gradient(
          to bottom,
          rgba(98, 122, 230, 0%) 0%,
          #627ae6 50%,
          rgba(98, 122, 230, 0%) 100%
        );
        box-shadow: 0 0 80px 20px rgba(98, 122, 230, 30%);
      }
    }

    .workflow-item {
      position: relative;
      z-index: 2;
      width: 100%;
      height: 100%;
      padding: 10px 20px 60px;
      background: linear-gradient(130deg, #000 1%, #222 114.3%);
      border-radius: 16px;
    }

    .workflow-item-box {
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      width: 100%;
      height: 100%;

      .item-number {
        position: absolute;
        top: 49px;
        left: -21px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 42px;
        height: 42px;
        color: #000;
        font-weight: 600;
        font-size: 20px;
        background: #4bb75a;
        border-radius: 100px;
        box-shadow: -4px 4px 8px 0 rgba(75, 183, 90, 30%);
      }
    }

    .workflow-image-box {
      position: relative;
      top: 15px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      width: 100%;
      height: 72%;
    }

    .workflow-image {
      width: auto;
      height: 100%;
      object-fit: contain;
    }

    .workflow-name {
      margin-top: 28px;
      font-weight: 600;
      font-size: 24px;
      line-height: 38px;
      white-space: pre;
      text-align: center;
      background: linear-gradient(
        88deg,
        #fff 2.84%,
        rgba(255, 255, 255, 80%) 76.02%
      );
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
}

@media (max-width: @small-screen-size) {
  .workflow-wrapper {
    margin-top: 208px;

    .workflow-container {
      padding: 0 !important;
    }

    .workflow-images-swiper {
      margin-top: 68px;
      padding-bottom: 38px;
    }

    .workflow-swiper-content {
      .swiper-slide {
        top: 0;
        right: 0;
        left: 0;
        transform: none;
        transition: none !important;

        &[data-swiper-slide-index='1'] {
          .workflow-image {
            width: 90% !important;
            margin: auto;
          }
        }
      }

      .workflow-item {
        .workflow-image {
          width: 100%;
          height: auto;
        }
      }
    }
  }
}
