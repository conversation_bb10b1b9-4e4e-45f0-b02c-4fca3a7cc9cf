.panel-wrapper {
  position: relative;
  padding: 134px 0;

  .panel-container {
    &::before {
      position: absolute;
      top: 60px;
      left: 50px;
      width: 171px;
      height: 185px;
      background: radial-gradient(
        ellipse at center,
        rgba(75, 183, 90, 30%) 60%,
        transparent 100%
      );
      filter: blur(100px);
      content: '';
    }

    &::after {
      position: absolute;
      right: 10px;
      bottom: 350px;
      z-index: 0;
      width: 89px;
      height: 300px;
      background: radial-gradient(
        ellipse at center,
        rgba(98, 122, 230, 30%) 30%,
        transparent 100%
      );
      filter: blur(50px);
      content: '';
    }
  }

  .panel-row {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    gap: 30px;

    &::before {
      position: absolute;
      top: 30px;
      left: 0;
      z-index: 2;
      width: 1px;
      height: 117px;
      background: linear-gradient(
        to bottom,
        rgba(75, 183, 90, 0%) 0%,
        rgba(75, 183, 90, 100%) 50%,
        rgba(75, 183, 90, 0%) 100%
      );
      content: '';
    }

    &::after {
      position: absolute;
      right: 0;
      bottom: 245px;
      z-index: 2;
      width: 1px;
      height: 117px;
      background: linear-gradient(
        to bottom,
        rgba(98, 122, 230, 0%) 0%,
        #627ae6 50%,
        rgba(98, 122, 230, 0%) 100%
      );
      content: '';
    }
  }

  .panel-item {
    position: relative;
    z-index: 1;
    width: calc(57% - 30px);
    height: 510px;
    padding: 45px 40px;
    background: linear-gradient(129deg, #000 0.67%, #222 116.37%);
    border: 1px solid rgba(255, 255, 255, 10%);
    border-radius: 16px;
    box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 25%);

    .panel-desc {
      position: absolute;
      bottom: 40px;
      left: 40px;
      font-weight: 800;
      font-size: 32px;
      font-family: Poppins-Regular;
      line-height: 1.4;
      background: linear-gradient(
        88deg,
        #fff 2.84%,
        rgba(255, 255, 255, 0.8) 76.02%
      );
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    &:first-child,
    &:last-child {
      width: 43%;
      padding-bottom: 165px;

      .panel-image {
        width: 100%;
        height: auto;
      }
    }

    &:first-child {
      padding: 0 0 45px;
      transition: all 0.5s;
      .panel-image-1-1 {
        position: absolute;
        top: 112px;
        left: 62px;
        width: 173px !important;
        height: 178px;
        z-index: 1;
        transform: scale(1) translateY(0);
        transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      }
      .panel-image-1-2 {
        position: absolute;
        top: 60px;
        left: 154px;
        width: 261px !important;
        height: 285px;
        z-index: 2;
        transform: scale(1) translateY(0);
        transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      }
      .panel-image-1-3 {
        position: absolute;
        top: 112px;
        right: 0;
        width: 148px !important;
        height: 274px;
        z-index: 1;
        transform: scale(1) translateY(0);
        transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      }
      &:hover {
        .panel-image-1-1 {
          z-index: 2;
          transform: scale(1.05) translateY(-5px);
          animation: floatUp 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }
        .panel-image-1-2 {
          z-index: 1;
          transform: scale(0.95) translateY(5px);
          opacity: 0.8;
        }
        .panel-image-1-3 {
          z-index: 2;
          transform: scale(1.05) translateY(-5px);
          animation: floatUp 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }
      }
    }

    &:nth-child(2) {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      background: linear-gradient(123deg, #000 1.15%, #222 105.64%);
      transition: all 0.5s;

      .panel-image {
        position: relative;
        top: -40px;
      }
      .panel-image-2-1 {
        top: -8px;
        left: 38px;
        width: 173px;
        height: 178px;
        z-index: 2;
      }
      .panel-image-2-2 {
        width: 320px;
        height: 274px;
        z-index: 1;
        opacity: 0;
        transform: translateY(20px) scale(0.95);
        transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      }
      &:hover {
        .panel-image-2-2 {
          opacity: 1;
          transform: translateY(0) scale(1);
          animation: slideInUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }
      }
    }

    &:nth-child(3) {
      .panel-desc {
        bottom: 35px;
      }
    }
  }
}

@media (max-width: @small-screen-size) {
  .panel-wrapper {
    padding: 50px 0;

    .panel-row {
      &::before,
      &::after {
        display: none;
      }
    }

    .panel-container {
      &::before,
      &::after {
        display: none;
      }
    }

    .panel-item {
      width: 100% !important;
      height: 630px !important;
      padding: 40px 30px;

      .panel-desc {
        right: 30px;
        left: 30px;
        font-size: 30px;
      }

      &:nth-child(2) {
        .panel-image {
          width: 100% !important;
        }
      }

      &:nth-child(3) {
        padding: 40px 20px;
      }
    }
  }
}

@media (max-width: @mini-screen-size) {
  .panel-wrapper {
    .panel-item {
      height: 437px !important;
    }
  }
}

// 动画关键帧定义
@keyframes slideInUp {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  50% {
    opacity: 0.7;
    transform: translateY(-5px) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes floatUp {
  0% {
    transform: scale(1) translateY(0);
  }
  50% {
    transform: scale(1.08) translateY(-8px);
  }
  100% {
    transform: scale(1.05) translateY(-5px);
  }
}
