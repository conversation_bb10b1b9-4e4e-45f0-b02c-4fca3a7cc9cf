import React from 'react';
import { graphql, useStaticQuery } from 'gatsby';
import {
  BANNER_DATA,
  V3_BANNER_DATA,
  LINK_DATA,
  BLOG_DETAIL_TRY_CARD_DATA,
  BLOG_REG_GUIDE_MODAL_DATA,
  FAQS_DATA,
  FAQS_DATA_MOBILE,
  EDU_OFFER_DETAILS_DATA,
  EDU_OFFER_EMAIL_TEMPLATE,
  EDU_OFFER_PARTICIPATE_DATA,
  EDU_OFFER_NOTES_DATA,
  EDU_OFFER_FAQS_DATA,
  EDU_OFFER_EMAIL_SUBJECT,
  BANNER_TITLE,
  FOOTER_DATA,
} from '../data/data';
import I18n from '../../../../common/components/i18n/i18n';
import ChatDOCLogo from '../../../../common/assets/logo/chatdoc.png';
import { GlobalContext } from '../../../../common/hooks/useGlobalHook';

const GlobalProvider = ({ children, blogValues }) => {
  const {
    notFoundImage,
    commentsBg,
    footerBg,
    footerBgMini,
    bannerImage,
    bannerVideoBgImage,
    allBannerVideoData,
    allCustomersData,
    allSmartDocPanelsData,
    allTapSourceData,
    allWorkflowData,
    allExploreData,
    allNicheData,
    bannerAndScenariosBg,
    bannerAndScenariosBgMini,
    blogBg,
    blogBgMini,
    blogCardBg,
    blogEmptyImage,
    blogRegGuideModalBg,
    gitCommitData,
    allUsersData,
    allScenariosData,
    allAdvantagesData,
    allDocumentsData,
    allFeaturesData,
    allExtensionData,
    allCommentsData,
    allPricingData,
    allStrapiChatdocChangelog,
    allEduOfferAdvantagesData,
    allEduOfferUniversitiesData,
    allEduOfferDiscountCodeJson,
    eduOfferParticipateBg,
    eduOfferFaqsBg,
    allStrapiChatdocGuide,
  } = useStaticQuery(query);

  const globalContextValues = {
    logo: ChatDOCLogo,
    bannerData: BANNER_DATA,
    bannerImage: bannerImage,
    v3BannerData: V3_BANNER_DATA,
    bannerTitle: BANNER_TITLE,
    bannerVideoBgImage: bannerVideoBgImage,
    allBannerVideoData: allBannerVideoData.nodes[0].childrenBannerVideoJson,
    footerData: FOOTER_DATA,
    allCustomersData: allCustomersData,
    allSmartDocPanelsData:
      allSmartDocPanelsData.nodes[0].childrenSmartDocPanelsJson,
    allTapSourceData: allTapSourceData.nodes[0].childrenTapSourceJson,
    allWorkflowData: allWorkflowData.nodes[0].childrenWorkflowJson,
    allExploreData: allExploreData.nodes[0].childrenExploreJson,
    allNicheData: allNicheData.nodes[0].childrenNicheJson,
    bannerAndScenariosBg: bannerAndScenariosBg,
    bannerAndScenariosBgMini: bannerAndScenariosBgMini,
    hideProductHunt: true,
    hideUsersInBanner: true,
    hidePaperInFooter: true,
    commentsBg: commentsBg,
    footerBg: footerBg,
    footerBgMini: footerBgMini,
    gitCommitData: gitCommitData,
    usersData: allUsersData.nodes,
    scenariosData: allScenariosData.nodes[0].childrenScenariosJson,
    advantagesData: allAdvantagesData.nodes[0].childrenAdvantagesJson,
    documentsData: allDocumentsData.nodes[0].childrenDocumentsJson,
    featuresData: allFeaturesData.nodes[0].childrenFeaturesJson,
    extensionData: allExtensionData.nodes[0].childrenExtensionJson[0],
    commentsData: allCommentsData.nodes[0].childrenCommentsJson,
    pricingData: allPricingData.nodes[0].childrenPricingJson,
    logData: allStrapiChatdocChangelog.nodes,
    linkData: LINK_DATA,
    linkButtonText: 'Try for Free',
    faqsData: FAQS_DATA,
    faqsMobileData: FAQS_DATA_MOBILE,
    notFoundImage: notFoundImage,
    blogBg: blogBg,
    blogBgMini: blogBgMini,
    blogCardBg: blogCardBg,
    blogEmptyImage: blogEmptyImage,
    blogRegGuideModalBg: blogRegGuideModalBg,
    eduOfferParticipateBg: eduOfferParticipateBg,
    eduOfferFaqsBg: eduOfferFaqsBg,
    blogDetailTryCardData: BLOG_DETAIL_TRY_CARD_DATA,
    blogRegGuideModalData: BLOG_REG_GUIDE_MODAL_DATA,
    blogArticles: blogValues.blogArticles,
    blogCategory: blogValues.blogCategory,
    blogList: blogValues.blogList,
    blogArticle: blogValues.blogArticle,
    blogTreeData: blogValues.blogTreeData,
    blogRecommendArticles: blogValues.blogRecommendArticles,
    eduOfferAdvantagesData:
      allEduOfferAdvantagesData.nodes[0].childrenEduOfferAdvantagesJson,
    eduOfferUniversitiesData:
      allEduOfferUniversitiesData.nodes[0].childrenEduOfferUniversitiesJson,
    eduOfferDetailsData: EDU_OFFER_DETAILS_DATA,
    eduOfferEmailSubject: EDU_OFFER_EMAIL_SUBJECT,
    eduOfferEmailTemplate: EDU_OFFER_EMAIL_TEMPLATE,
    eduOfferParticipateData: EDU_OFFER_PARTICIPATE_DATA,
    eduOfferDiscountCodeData: allEduOfferDiscountCodeJson.nodes[0],
    eduOfferNotesData: EDU_OFFER_NOTES_DATA,
    eduOfferFaqsData: EDU_OFFER_FAQS_DATA,
    guideData: allStrapiChatdocGuide.nodes,
  };

  return (
    <GlobalContext.Provider value={globalContextValues}>
      <I18n>{children}</I18n>
    </GlobalContext.Provider>
  );
};

const query = graphql`
  query {
    gitCommitData: gitCommit(latest: { eq: true }) {
      hash
      date
    }
    notFoundImage: file(base: { eq: "404.png" }) {
      childImageSharp {
        gatsbyImageData
      }
    }
    bannerVideoBgImage: file(base: { eq: "banner-video-bg.png" }) {
      childImageSharp {
        gatsbyImageData
      }
    }

    allBannerVideoData: allFile(
      filter: { absolutePath: { regex: "/banner-video/banner-video/" } }
    ) {
      nodes {
        id
        childrenBannerVideoJson {
          name
          video {
            publicURL
          }
          poster {
            name
            publicURL
            childImageSharp {
              gatsbyImageData
            }
          }
        }
      }
    }

    allCustomersData: allFile(
      filter: { absolutePath: { regex: "/customers/images/" } }
      sort: { name: ASC }
    ) {
      nodes {
        name
        childImageSharp {
          gatsbyImageData
        }
      }
    }

    allSmartDocPanelsData: allFile(
      filter: { absolutePath: { regex: "/smart-doc-panels/smart-doc-panels/" } }
    ) {
      nodes {
        id
        childrenSmartDocPanelsJson {
          desc
          images {
            name
            image {
              childImageSharp {
                gatsbyImageData
              }
            }
          }
          imagesList {
            name
            image {
              childImageSharp {
                gatsbyImageData
              }
            }
            icon {
              publicURL
            }
          }
        }
      }
    }

    allTapSourceData: allFile(
      filter: { absolutePath: { regex: "/tap-source/tap-source/" } }
    ) {
      nodes {
        id
        childrenTapSourceJson {
          name
          image {
            childImageSharp {
              gatsbyImageData
            }
          }
        }
      }
    }

    allWorkflowData: allFile(
      filter: { absolutePath: { regex: "/workflow/workflow/" } }
    ) {
      nodes {
        id
        childrenWorkflowJson {
          name
          image {
            publicURL
          }
        }
      }
    }

    allExploreData: allFile(
      filter: { absolutePath: { regex: "/explore/explore/" } }
    ) {
      nodes {
        id
        childrenExploreJson {
          name
          image {
            childImageSharp {
              gatsbyImageData
            }
          }
        }
      }
    }

    allNicheData: allFile(
      filter: { absolutePath: { regex: "/niche/niche/" } }
    ) {
      nodes {
        id
        childrenNicheJson {
          name
          desc
          image {
            childImageSharp {
              gatsbyImageData
            }
          }
          link
        }
      }
    }

    bannerImage: file(base: { eq: "banner.png" }) {
      childImageSharp {
        gatsbyImageData
      }
    }
    bannerAndScenariosBg: file(base: { eq: "banner-and-scenarios-bg.png" }) {
      childImageSharp {
        gatsbyImageData
      }
    }
    bannerAndScenariosBgMini: file(
      base: { eq: "banner-and-scenarios-bg-mini.png" }
    ) {
      childImageSharp {
        gatsbyImageData
      }
    }
    commentsBg: file(base: { eq: "comments-bg.png" }) {
      childImageSharp {
        gatsbyImageData
      }
    }
    footerBg: file(base: { eq: "footer-bg.png" }) {
      childImageSharp {
        gatsbyImageData
      }
    }
    footerBgMini: file(base: { eq: "footer-bg-mini.png" }) {
      childImageSharp {
        gatsbyImageData
      }
    }
    blogBg: file(base: { eq: "blog-bg.png" }) {
      childImageSharp {
        gatsbyImageData
      }
    }
    blogBgMini: file(base: { eq: "blog-bg-mini.png" }) {
      childImageSharp {
        gatsbyImageData
      }
    }
    blogCardBg: file(base: { eq: "blog-card-bg.png" }) {
      childImageSharp {
        gatsbyImageData
      }
    }
    blogRegGuideModalBg: file(base: { eq: "reg-guide-modal-bg.png" }) {
      childImageSharp {
        gatsbyImageData
      }
    }
    eduOfferParticipateBg: file(base: { eq: "participate-bg.png" }) {
      childImageSharp {
        gatsbyImageData
      }
    }
    eduOfferFaqsBg: file(base: { eq: "faqs-bg.png" }) {
      childImageSharp {
        gatsbyImageData
      }
    }
    blogEmptyImage: file(base: { eq: "blog-empty.png" }) {
      childImageSharp {
        gatsbyImageData
      }
    }
    allUsersData: allFile(
      filter: { absolutePath: { regex: "/users/images/" } }
      sort: { name: ASC }
    ) {
      nodes {
        name
        childImageSharp {
          gatsbyImageData
        }
      }
    }
    allScenariosData: allFile(
      filter: { absolutePath: { regex: "/scenarios/scenarios/" } }
    ) {
      nodes {
        id
        childrenScenariosJson {
          name
          title
          desc
          icon {
            name
            childImageSharp {
              gatsbyImageData
            }
          }
          video {
            publicURL
          }
          poster {
            name
            publicURL
            childImageSharp {
              gatsbyImageData
            }
          }
        }
      }
    }
    allAdvantagesData: allFile(
      filter: { absolutePath: { regex: "/advantages/advantages/" } }
    ) {
      nodes {
        id
        childrenAdvantagesJson {
          title
          info
          icon {
            name
            childImageSharp {
              gatsbyImageData
            }
          }
        }
      }
    }
    allDocumentsData: allFile(
      filter: { absolutePath: { regex: "/documents/documents/" } }
    ) {
      nodes {
        id
        childrenDocumentsJson {
          info
          desc
          documentId
          image {
            name
            childImageSharp {
              gatsbyImageData
            }
          }
        }
      }
    }
    allFeaturesData: allFile(
      filter: { absolutePath: { regex: "/features/features/" } }
    ) {
      nodes {
        id
        childrenFeaturesJson {
          name
          title
          subtitle
          icon {
            childImageSharp {
              gatsbyImageData
            }
          }
          descList {
            desc
            video {
              publicURL
            }
            poster {
              name
              publicURL
              childImageSharp {
                gatsbyImageData
              }
            }
            isNew
          }
        }
      }
    }
    allExtensionData: allFile(
      filter: { absolutePath: { regex: "/extension/extension/" } }
    ) {
      nodes {
        childrenExtensionJson {
          title
          descList
          image {
            name
            childImageSharp {
              gatsbyImageData
            }
          }
          icon {
            name
            childImageSharp {
              gatsbyImageData
            }
          }
          bg {
            childImageSharp {
              gatsbyImageData
            }
          }
          bgMini {
            childImageSharp {
              gatsbyImageData
            }
          }
        }
      }
    }
    allCommentsData: allFile(
      filter: { absolutePath: { regex: "/comments/comments/" } }
    ) {
      nodes {
        id
        childrenCommentsJson {
          link
          image {
            name
            childImageSharp {
              gatsbyImageData
            }
          }
          alt
        }
      }
    }
    allPricingData: allFile(
      filter: { absolutePath: { regex: "/pricing/pricing/" } }
    ) {
      nodes {
        id
        childrenPricingJson {
          alias
          buttonTitle
          icon {
            childImageSharp {
              gatsbyImageData
            }
          }
          supportGPT4
          supportType
          supportFormulaRecognition
          params {
            package_type
          }
        }
      }
    }
    allStrapiChatdocChangelog(sort: { publishAt: DESC }) {
      nodes {
        id
        title
        publishAt
        content {
          data {
            childMarkdownRemark {
              html
              rawMarkdownBody
              frontmatter {
                version
                date
              }
            }
          }
          medias {
            alternativeText
            file {
              alternativeText
            }
            localFile {
              childImageSharp {
                gatsbyImageData
              }
            }
            src
            url
          }
        }
        videos {
          localFile {
            publicURL
          }
        }
      }
    }
    allEduOfferAdvantagesData: allFile(
      filter: {
        absolutePath: { regex: "/edu-offer-advantages/edu-offer-advantages/" }
      }
    ) {
      nodes {
        id
        childrenEduOfferAdvantagesJson {
          title
          info
          icon {
            name
            childImageSharp {
              gatsbyImageData
            }
          }
        }
      }
    }
    allEduOfferUniversitiesData: allFile(
      filter: {
        absolutePath: {
          regex: "/edu-offer-universities/edu-offer-universities/"
        }
      }
    ) {
      nodes {
        id
        childrenEduOfferUniversitiesJson {
          name
          logo {
            name
            childImageSharp {
              gatsbyImageData
            }
          }
        }
      }
    }
    allEduOfferDiscountCodeJson(filter: {}) {
      nodes {
        title
        step1
        step3 {
          title
          discount
        }
        step2 {
          title
          extra {
            text
            img {
              childImageSharp {
                gatsbyImageData
              }
              name
            }
          }
        }
      }
    }
    allStrapiChatdocGuide(sort: { order: ASC }) {
      nodes {
        id
        title
        slug
        type
        order
        content {
          data {
            childMarkdownRemark {
              html
              rawMarkdownBody
            }
          }
        }
        parents {
          id
          title
        }
        seo {
          metaTitle
          metaDescription
          keywords
        }
      }
    }
  }
`;

export default GlobalProvider;
