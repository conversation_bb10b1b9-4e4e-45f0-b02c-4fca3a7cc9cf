import React, { useState, useEffect } from 'react';
import { Container } from 'react-bootstrap';
import { useGlobalContext } from '../../../../common/hooks/useGlobalHook';
import '../styles/niche.less';
import { GatsbyImage } from 'gatsby-plugin-image';
import { GLOBAL_USERS_NUMBER } from '../../../../common/data/chatdoc/data';
import { useMobilePageContext } from '../../../../common/hooks/useMobilePageContext';

const Niche = () => {
  const { allNicheData } = useGlobalContext();
  const isMobile = useMobilePageContext();
  const [nicheDataArray, setNicheDataArray] = useState([]);

  const handleMouseEnter = ({ event, listIndex, index }) => {
    if (isMobile) {
      return;
    }
    document.querySelectorAll(`.niche-item`).forEach((item) => {
      item.classList.remove('niche-item--lefted', 'niche-item--expanded');
      item.classList.remove('niche-item--expanded');
    });
    const nicheItem = event.target.closest('.niche-item');
    let currentColLast;
    if (index < 3) {
      currentColLast = document.getElementById(`niche-item-${listIndex}-3`);
    } else {
      currentColLast = document.getElementById(`niche-item-${listIndex}-2`);
    }

    nicheItem.classList.add('niche-item--expanded');
    currentColLast.classList.add('niche-item--lefted');
  };
  const handleMouseLeave = (event) => {
    if (isMobile) {
      return;
    }
    const nicheItem = event.target.closest('.niche-item');
    nicheItem.classList.remove('niche-item--expanded');
    const leftedEl = document.querySelector(`.niche-item--lefted`);
    leftedEl?.classList.remove('niche-item--lefted');
  };

  const handleClickItem = (listIndex, index, item) => {
    if (isMobile) {
      document.querySelectorAll(`.niche-item`).forEach((item) => {
        item.classList.remove('niche-item--expanded');
      });
      const currentItem = document.getElementById(
        `niche-item-${listIndex}-${index}`,
      );
      const nicheItem = currentItem.closest('.niche-item');
      nicheItem.classList.add('niche-item--expanded');
    } else {
      window.open(item.link, '_blank');
    }
  };

  useEffect(() => {
    const nicheDataArray1 = [];
    const nicheDataArray2 = [];
    for (let i = 0; i < allNicheData.length; i++) {
      if (i < 4) {
        nicheDataArray1.push(allNicheData[i]);
      } else {
        nicheDataArray2.push(allNicheData[i]);
      }
    }
    setNicheDataArray([nicheDataArray1, nicheDataArray2]);
  }, [allNicheData]);

  return (
    <div className="niche-wrapper">
      <Container className="niche-container">
        <div className="niche-content">
          <div className="niche-title">
            <h1 className="module-title">
              Loved by {GLOBAL_USERS_NUMBER}+ Global Users
            </h1>
            <p>Ready for a productivity boost? Try ChatDOC today!</p>
          </div>
          <div className="niche-list-content">
            {nicheDataArray.map((list, listIndex) => (
              <div
                className="niche-list"
                id={`niche-list-${listIndex}`}
                key={listIndex}>
                {list.map((item, index) => (
                  <div
                    key={item.name}
                    className="niche-item"
                    id={`niche-item-${listIndex}-${index}`}
                    onMouseEnter={(event) =>
                      handleMouseEnter({ event, listIndex, index })
                    }
                    onMouseLeave={handleMouseLeave}
                    onClick={() => handleClickItem(listIndex, index, item)}>
                    <div className="niche-item-box">
                      <div className="niche-info">
                        <h3>{item.name}</h3>
                        <p>{item.desc}</p>
                      </div>
                      <div className="niche-image">
                        <GatsbyImage
                          image={item.image.childImageSharp.gatsbyImageData}
                          alt={item.name}
                          className="niche-image"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ))}
          </div>
        </div>
      </Container>
    </div>
  );
};

export default Niche;
