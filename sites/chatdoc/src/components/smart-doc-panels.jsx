import React from 'react';
import { Container } from 'react-bootstrap';
import { GatsbyImage } from 'gatsby-plugin-image';
import { useGlobalContext } from '../../../../common/hooks/useGlobalHook';
import AutoSwitchCards from './auto-switch-cards';

import '../styles/smart-doc-panels.less';
import classnames from 'classnames';

const SmartDocPanels = () => {
  const { allSmartDocPanelsData } = useGlobalContext();
  console.log(allSmartDocPanelsData);
  return (
    <div className="panel-wrapper">
      <Container className="panel-container">
        <div className="panel-row">
          {allSmartDocPanelsData.map((item, index) => (
            <div className="panel-item" key={index}>
              {item.images ? (
                item.images.map(imageItem => (
                  <GatsbyImage
                    key={imageItem.name}
                    image={imageItem.image.childImageSharp.gatsbyImageData}
                    alt=""
                    className={classnames({
                      'panel-image': true,
                      [`panel-image-${imageItem.name}`]: true,
                    })}
                  />
                ))
              ) : (
                <AutoSwitchCards itemList={item.imagesList} />
              )}
              <pre className="panel-desc">{item.desc}</pre>
            </div>
          ))}
        </div>
      </Container>
    </div>
  );
};

export default SmartDocPanels;
