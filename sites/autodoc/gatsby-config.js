require("dotenv").config({
  path: `.env.${process.env.SITE_ENV}`,
})

const path = require('path');
const site = 'autodoc';

const { getEnvVariables } = require("../../common/utils/env.js");
const { isVipSite, isNextSite, baiduId, productUrl } = getEnvVariables();

function getProxy() {
  if (isVipSite) {
    return 'https://autodoc-test.test.paodingai.com/';
  } else if (isNextSite) {
    return 'https://autodoc-next.paodingai.com/';
  } else {
    return 'http://autodoc.dev.cheftin.cn/';
  }
}

function createBuildRoot(site) {
  if (site) {
    return `${__dirname}/src/pages`;
  }
  return '';
}

const manifest = require(`${__dirname}/src/manifest.js`) || {};
var { createProxyMiddleware } = require('http-proxy-middleware');

const plugins = [
  {
    resolve: 'gatsby-plugin-page-creator',
    options: {
      path: createBuildRoot(site),
      // ignore: ['manifest.js', '**/components/*']
    },
  },
  'gatsby-transformer-json',
  {
    resolve: `gatsby-source-filesystem`,
    options: {
      path: `${__dirname}/static`,
    },
  },
  {
    resolve: `gatsby-source-filesystem`,
    options: {
      path: `${__dirname}/content`,
    },
  },
  {
    resolve: `gatsby-source-filesystem`,
    options: {
      path: path.resolve(__dirname, '../../common/content'),
    },
  },
  {
    resolve: `gatsby-transformer-remark`,
    options: {
      // Footnotes mode (default: true)
      footnotes: true,
      // GitHub Flavored Markdown mode (default: true)
      gfm: true,
      // Plugins configs
      plugins: [
        {
          resolve: `gatsby-remark-images`,
          options: {
            // It's important to specify the maxWidth (in pixels) of
            // the content container as this plugin uses this as the
            // base for generating different widths of each image.
            quality: 100,
            wrapperStyle:
              'max-width: 100%!important; border: 1px solid #EAEDF3;',
          },
        },
        {
          resolve: 'gatsby-remark-external-links', // markdown 链接默认在新窗口打开
        },
        {
          resolve: `gatsby-remark-highlight-code`,
        },
      ],
    },
  },
  `gatsby-plugin-image`,
  {
    resolve: 'gatsby-plugin-react-svg',
    options: {
      rule: {
        include: [
          path.resolve(__dirname, 'src/assets/icons'),
          path.resolve(__dirname, '../../common/assets/icons'),
        ],
      },
    },
  },
  {
    resolve: `gatsby-transformer-sharp`,
    options: {
      checkSupportedExtensions: false,
    },
  },
  {
    resolve: `gatsby-plugin-sharp`,
    options: {
      defaults: {
        placeholder: 'none',
      },
    },
  },
  {
    resolve: `gatsby-plugin-less`,
    options: {
      lessOptions: {
        modifyVars: require(`../../common/styles/theme.js`),
      },
    },
  },
  {
    resolve: `gatsby-plugin-postcss`,
    options: {
      postCssPlugins: [
        require(`postcss-preset-env`)({
          stage: 0,
        }),
        require('postcss-nested'),
        require('postcss-calc'),
      ],
      cssLoaderOptions: {
        camelCase: false,
      },
    },
  },
  {
    resolve: 'gatsby-plugin-no-sourcemaps',
  },
  `gatsby-plugin-sitemap`,
  {
    resolve: 'gatsby-plugin-robots-txt',
    options: {
      policy: [{ userAgent: '*', disallow: ['/recovery'] }],
    },
  },
  'gatsby-plugin-remove-serviceworker',
  'gatsby-source-local-git',
];

if (baiduId) {
  plugins.push({
    resolve: `gatsby-plugin-baidu-analytics`,
    options: { siteId: baiduId, head: false },
  });
}

module.exports = {
  assetPrefix: `https://cdn.paodingai.com/${site}`,
  siteMetadata: {
    currentSite: site,
    siteUrl: productUrl || 'https://www.paodingai.com/',
  },
  plugins,
  developMiddleware: (app) => {
    app.use(
      '/api/v1',
      createProxyMiddleware({
        target: getProxy() || '/',
        changeOrigin: true,
        secure: false, // Do not reject self-signed certificates.
      }),
    );
    app.use(
      '/crm',
      createProxyMiddleware({
        target: getProxy() || '/',
        changeOrigin: true,
        secure: false, // Do not reject self-signed certificates.
      }),
    );
  },
};
